@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    /* Card colors */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    
    /* Popover colors */
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    /* Primary colors - Modern blue */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    
    /* Secondary colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    /* Muted colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    /* Accent colors */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    /* Border and input */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* Success colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    
    /* Warning colors */
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    
    /* Info colors */
    --info: 199.4 89.2% 48.4%;
    --info-foreground: 210 40% 98%;
    
    /* Calorie tracking specific colors */
    --calories-consumed: 217.2 91.2% 59.8%; /* Blue */
    --calories-burned: 142.1 76.2% 36.3%; /* Green */
    --calories-remaining: 47.9 95.8% 53.1%; /* Yellow */
    --calories-excess: 0 84.2% 60.2%; /* Red */
    
    /* Chart colors */
    --chart-1: 217.2 91.2% 59.8%;
    --chart-2: 142.1 76.2% 36.3%;
    --chart-3: 47.9 95.8% 53.1%;
    --chart-4: 270 95.2% 75.3%;
    --chart-5: 340.7 82.2% 67.5%;
    
    /* Border radius */
    --radius: 0.75rem;
  }

  .light {
    /* Light theme overrides */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    overscroll-behavior-y: none;
  }
  
  /* Improved scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20;
  }

  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
}

@layer components {
  /* Typography hierarchy */
  .heading-1 {
    @apply text-4xl font-bold tracking-tight lg:text-5xl;
  }

  .heading-2 {
    @apply text-3xl font-semibold tracking-tight lg:text-4xl;
  }

  .heading-3 {
    @apply text-2xl font-semibold tracking-tight lg:text-3xl;
  }

  .heading-4 {
    @apply text-xl font-semibold tracking-tight lg:text-2xl;
  }

  .heading-5 {
    @apply text-lg font-semibold tracking-tight lg:text-xl;
  }

  .heading-6 {
    @apply text-base font-semibold tracking-tight lg:text-lg;
  }

  .body-large {
    @apply text-lg leading-relaxed;
  }

  .body-default {
    @apply text-base leading-relaxed;
  }

  .body-small {
    @apply text-sm leading-relaxed;
  }

  .caption {
    @apply text-xs text-muted-foreground;
  }

  .overline {
    @apply text-xs font-medium uppercase tracking-wider text-muted-foreground;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success) / 0.8) 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(var(--warning) / 0.8) 100%);
  }

  /* Enhanced shadows */
  .shadow-glow {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  .shadow-success-glow {
    box-shadow: 0 0 20px hsl(var(--success) / 0.3);
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-muted/50 rounded;
  }

  /* Improved animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Layout utilities */
  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }

  .section-spacing {
    @apply py-8 md:py-12 lg:py-16;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Responsive spacing utilities */
  .space-y-responsive > * + * {
    @apply mt-4 md:mt-6 lg:mt-8;
  }

  .gap-responsive {
    @apply gap-4 md:gap-6 lg:gap-8;
  }

  .padding-responsive {
    @apply p-4 md:p-6 lg:p-8;
  }

  .margin-responsive {
    @apply m-4 md:m-6 lg:m-8;
  }

  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }

  .interactive:hover {
    @apply scale-[1.02];
  }

  .interactive:active {
    @apply scale-[0.98];
  }

  /* Enhanced focus states */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Status indicators */
  .status-success {
    @apply bg-success/10 text-success border-success/20;
  }

  .status-warning {
    @apply bg-warning/10 text-warning border-warning/20;
  }

  .status-error {
    @apply bg-destructive/10 text-destructive border-destructive/20;
  }

  .status-info {
    @apply bg-info/10 text-info border-info/20;
  }

  /* Layout utilities */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Grid utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Truncate text */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes floatSubtle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes floatStrong {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-12px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.5);
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.8);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
