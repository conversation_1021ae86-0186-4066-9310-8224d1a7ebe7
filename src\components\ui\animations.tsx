import React from 'react'
import { cn } from '@/src/lib/utils'

interface FadeInProps {
  children: React.ReactNode
  className?: string
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right'
}

export const FadeIn: React.FC<FadeInProps> = ({ 
  children, 
  className, 
  delay = 0, 
  duration = 500,
  direction = 'up'
}) => {
  const directionClasses = {
    up: 'translate-y-4',
    down: '-translate-y-4',
    left: 'translate-x-4',
    right: '-translate-x-4'
  }

  return (
    <div
      className={cn(
        'animate-fade-in opacity-0',
        directionClasses[direction],
        className
      )}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`,
        animationFillMode: 'forwards'
      }}
    >
      {children}
    </div>
  )
}

interface SlideInProps {
  children: React.ReactNode
  className?: string
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right'
}

export const SlideIn: React.FC<SlideInProps> = ({ 
  children, 
  className, 
  delay = 0, 
  duration = 300,
  direction = 'up'
}) => {
  const directionClasses = {
    up: 'animate-slide-up',
    down: 'animate-slide-down',
    left: 'animate-slide-left',
    right: 'animate-slide-right'
  }

  return (
    <div
      className={cn(
        directionClasses[direction],
        className
      )}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  )
}

interface ScaleInProps {
  children: React.ReactNode
  className?: string
  delay?: number
  duration?: number
}

export const ScaleIn: React.FC<ScaleInProps> = ({ 
  children, 
  className, 
  delay = 0, 
  duration = 200
}) => {
  return (
    <div
      className={cn('animate-scale-in', className)}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  )
}

interface StaggeredListProps {
  children: React.ReactNode[]
  className?: string
  staggerDelay?: number
  itemClassName?: string
}

export const StaggeredList: React.FC<StaggeredListProps> = ({ 
  children, 
  className, 
  staggerDelay = 100,
  itemClassName
}) => {
  return (
    <div className={className}>
      {React.Children.map(children, (child, index) => (
        <FadeIn 
          key={index} 
          delay={index * staggerDelay}
          className={itemClassName}
        >
          {child}
        </FadeIn>
      ))}
    </div>
  )
}

interface PulseProps {
  children: React.ReactNode
  className?: string
  intensity?: 'subtle' | 'normal' | 'strong'
}

export const Pulse: React.FC<PulseProps> = ({ 
  children, 
  className, 
  intensity = 'normal'
}) => {
  const intensityClasses = {
    subtle: 'animate-pulse-slow',
    normal: 'animate-pulse',
    strong: 'animate-ping'
  }

  return (
    <div className={cn(intensityClasses[intensity], className)}>
      {children}
    </div>
  )
}

interface BounceProps {
  children: React.ReactNode
  className?: string
  trigger?: boolean
}

export const Bounce: React.FC<BounceProps> = ({ 
  children, 
  className, 
  trigger = false
}) => {
  return (
    <div className={cn(trigger && 'animate-bounce', className)}>
      {children}
    </div>
  )
}

interface FloatingProps {
  children: React.ReactNode
  className?: string
  intensity?: 'subtle' | 'normal' | 'strong'
}

export const Floating: React.FC<FloatingProps> = ({ 
  children, 
  className, 
  intensity = 'normal'
}) => {
  const intensityClasses = {
    subtle: 'animate-float-subtle',
    normal: 'animate-float',
    strong: 'animate-float-strong'
  }

  return (
    <div className={cn(intensityClasses[intensity], className)}>
      {children}
    </div>
  )
}

interface CountUpProps {
  value: number
  duration?: number
  className?: string
  formatter?: (value: number) => string
}

export const CountUp: React.FC<CountUpProps> = ({ 
  value, 
  duration = 1000, 
  className,
  formatter = (v) => v.toString()
}) => {
  const [displayValue, setDisplayValue] = React.useState(0)

  React.useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      setDisplayValue(Math.floor(value * easeOutQuart))

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [value, duration])

  return (
    <span className={className}>
      {formatter(displayValue)}
    </span>
  )
}

interface TypewriterProps {
  text: string
  speed?: number
  className?: string
  onComplete?: () => void
}

export const Typewriter: React.FC<TypewriterProps> = ({ 
  text, 
  speed = 50, 
  className,
  onComplete
}) => {
  const [displayText, setDisplayText] = React.useState('')
  const [currentIndex, setCurrentIndex] = React.useState(0)

  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, speed)

      return () => clearTimeout(timeout)
    } else if (onComplete) {
      onComplete()
    }
  }, [currentIndex, text, speed, onComplete])

  return (
    <span className={className}>
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  )
}

interface ProgressiveBlurProps {
  children: React.ReactNode
  isBlurred: boolean
  className?: string
}

export const ProgressiveBlur: React.FC<ProgressiveBlurProps> = ({ 
  children, 
  isBlurred, 
  className 
}) => {
  return (
    <div 
      className={cn(
        'transition-all duration-500',
        isBlurred ? 'blur-sm opacity-50' : 'blur-0 opacity-100',
        className
      )}
    >
      {children}
    </div>
  )
}

interface ParallaxProps {
  children: React.ReactNode
  offset?: number
  className?: string
}

export const Parallax: React.FC<ParallaxProps> = ({ 
  children, 
  offset = 0.5, 
  className 
}) => {
  const [scrollY, setScrollY] = React.useState(0)
  const ref = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const handleScroll = () => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect()
        const scrolled = window.scrollY
        const rate = scrolled * -offset
        setScrollY(rate)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [offset])

  return (
    <div 
      ref={ref}
      className={className}
      style={{
        transform: `translateY(${scrollY}px)`
      }}
    >
      {children}
    </div>
  )
}
