import React, { Suspense, lazy } from 'react'
import { useLazyLoad } from '@/src/lib/performance'
import { LoadingSpinner, LoadingSkeleton } from './loading'
import { cn } from '@/src/lib/utils'

interface LazyComponentProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
  threshold?: number
}

export const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = <LoadingSpinner />,
  className,
  threshold = 0.1
}) => {
  const [ref, isVisible] = useLazyLoad(threshold)

  return (
    <div ref={ref} className={className}>
      {isVisible ? children : fallback}
    </div>
  )
}

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  placeholder?: string
  className?: string
  containerClassName?: string
  threshold?: number
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  className,
  containerClassName,
  threshold = 0.1,
  ...props
}) => {
  const [ref, isVisible] = useLazyLoad(threshold)
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [error, setError] = React.useState(false)

  const handleLoad = () => setIsLoaded(true)
  const handleError = () => setError(true)

  return (
    <div ref={ref} className={cn('relative overflow-hidden', containerClassName)}>
      {!isVisible && (
        <div className={cn('bg-muted animate-pulse', className)} />
      )}
      
      {isVisible && !error && (
        <>
          {placeholder && !isLoaded && (
            <img
              src={placeholder}
              alt=""
              className={cn('absolute inset-0 w-full h-full object-cover blur-sm', className)}
              aria-hidden="true"
            />
          )}
          <img
            src={src}
            alt={alt}
            className={cn(
              'transition-opacity duration-300',
              isLoaded ? 'opacity-100' : 'opacity-0',
              className
            )}
            onLoad={handleLoad}
            onError={handleError}
            {...props}
          />
        </>
      )}
      
      {error && (
        <div className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground',
          className
        )}>
          <span className="text-sm">Failed to load image</span>
        </div>
      )}
    </div>
  )
}

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  gap?: number
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className,
  gap = 0
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = React.useState(0)
  
  const startIndex = Math.floor(scrollTop / (itemHeight + gap))
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / (itemHeight + gap)) + 1,
    items.length
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * (itemHeight + gap)
  const offsetY = startIndex * (itemHeight + gap)

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={startIndex + index}
              style={{ 
                height: itemHeight,
                marginBottom: gap
              }}
            >
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

interface LazyRouteProps {
  component: () => Promise<{ default: React.ComponentType<any> }>
  fallback?: React.ReactNode
  errorBoundary?: React.ComponentType<{ error: Error; retry: () => void }>
}

export const LazyRoute: React.FC<LazyRouteProps> = ({
  component,
  fallback = <LoadingSkeleton lines={5} />,
  errorBoundary: ErrorBoundary
}) => {
  const LazyComponent = lazy(component)
  
  return (
    <Suspense fallback={fallback}>
      {ErrorBoundary ? (
        <ErrorBoundary error={new Error('Component failed to load')} retry={() => window.location.reload()}>
          <LazyComponent />
        </ErrorBoundary>
      ) : (
        <LazyComponent />
      )}
    </Suspense>
  )
}

interface IntersectionObserverProps {
  children: React.ReactNode
  onIntersect: () => void
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  className?: string
}

export const IntersectionObserver: React.FC<IntersectionObserverProps> = ({
  children,
  onIntersect,
  threshold = 0.1,
  rootMargin = '0px',
  triggerOnce = true,
  className
}) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const [hasTriggered, setHasTriggered] = React.useState(false)

  React.useEffect(() => {
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!triggerOnce || !hasTriggered)) {
          onIntersect()
          if (triggerOnce) {
            setHasTriggered(true)
            observer.disconnect()
          }
        }
      },
      { threshold, rootMargin }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [onIntersect, threshold, rootMargin, triggerOnce, hasTriggered])

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  )
}

interface ProgressiveImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  placeholder: string
  alt: string
  className?: string
}

export const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  placeholder,
  alt,
  className,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [currentSrc, setCurrentSrc] = React.useState(placeholder)

  React.useEffect(() => {
    const img = new Image()
    img.onload = () => {
      setCurrentSrc(src)
      setIsLoaded(true)
    }
    img.src = src
  }, [src])

  return (
    <img
      src={currentSrc}
      alt={alt}
      className={cn(
        'transition-all duration-500',
        !isLoaded && 'blur-sm scale-105',
        className
      )}
      {...props}
    />
  )
}
