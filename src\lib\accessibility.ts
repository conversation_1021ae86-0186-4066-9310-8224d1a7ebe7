import React, { useEffect, useRef, useState } from 'react'

// Focus management utilities
export const useFocusTrap = (isActive: boolean) => {
  const containerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!isActive || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus()
          e.preventDefault()
        }
      }
    }

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Dispatch custom event for parent to handle
        container.dispatchEvent(new CustomEvent('escape-pressed'))
      }
    }

    container.addEventListener('keydown', handleTabKey)
    container.addEventListener('keydown', handleEscapeKey)
    
    // Focus first element when trap becomes active
    firstElement?.focus()

    return () => {
      container.removeEventListener('keydown', handleTabKey)
      container.removeEventListener('keydown', handleEscapeKey)
    }
  }, [isActive])

  return containerRef
}

// Keyboard navigation utilities
export const useKeyboardNavigation = (
  items: HTMLElement[],
  orientation: 'horizontal' | 'vertical' = 'vertical'
) => {
  const handleKeyDown = (e: KeyboardEvent, currentIndex: number) => {
    let nextIndex = currentIndex

    switch (e.key) {
      case 'ArrowDown':
        if (orientation === 'vertical') {
          nextIndex = (currentIndex + 1) % items.length
          e.preventDefault()
        }
        break
      case 'ArrowUp':
        if (orientation === 'vertical') {
          nextIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1
          e.preventDefault()
        }
        break
      case 'ArrowRight':
        if (orientation === 'horizontal') {
          nextIndex = (currentIndex + 1) % items.length
          e.preventDefault()
        }
        break
      case 'ArrowLeft':
        if (orientation === 'horizontal') {
          nextIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1
          e.preventDefault()
        }
        break
      case 'Home':
        nextIndex = 0
        e.preventDefault()
        break
      case 'End':
        nextIndex = items.length - 1
        e.preventDefault()
        break
    }

    if (nextIndex !== currentIndex) {
      items[nextIndex]?.focus()
    }
  }

  return handleKeyDown
}

// Screen reader utilities
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.setAttribute('class', 'sr-only')
  announcement.textContent = message

  document.body.appendChild(announcement)

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// ARIA utilities
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

export const getAriaProps = (
  element: 'button' | 'input' | 'select' | 'dialog' | 'menu' | 'menuitem' | 'tab' | 'tabpanel',
  options: {
    label?: string
    labelledBy?: string
    describedBy?: string
    expanded?: boolean
    selected?: boolean
    checked?: boolean
    disabled?: boolean
    required?: boolean
    invalid?: boolean
    controls?: string
    owns?: string
    hasPopup?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog'
    level?: number
    setSize?: number
    posInSet?: number
  } = {}
) => {
  const props: Record<string, any> = {}

  // Common ARIA attributes
  if (options.label) props['aria-label'] = options.label
  if (options.labelledBy) props['aria-labelledby'] = options.labelledBy
  if (options.describedBy) props['aria-describedby'] = options.describedBy
  if (options.disabled) props['aria-disabled'] = options.disabled
  if (options.required) props['aria-required'] = options.required
  if (options.invalid) props['aria-invalid'] = options.invalid

  // Element-specific attributes
  switch (element) {
    case 'button':
      if (options.expanded !== undefined) props['aria-expanded'] = options.expanded
      if (options.controls) props['aria-controls'] = options.controls
      if (options.hasPopup) props['aria-haspopup'] = options.hasPopup
      break

    case 'input':
    case 'select':
      if (options.invalid) props['aria-invalid'] = options.invalid
      if (options.required) props['aria-required'] = options.required
      break

    case 'dialog':
      props['role'] = 'dialog'
      props['aria-modal'] = 'true'
      break

    case 'menu':
      props['role'] = 'menu'
      break

    case 'menuitem':
      props['role'] = 'menuitem'
      if (options.disabled) props['aria-disabled'] = options.disabled
      break

    case 'tab':
      props['role'] = 'tab'
      if (options.selected !== undefined) props['aria-selected'] = options.selected
      if (options.controls) props['aria-controls'] = options.controls
      break

    case 'tabpanel':
      props['role'] = 'tabpanel'
      if (options.labelledBy) props['aria-labelledby'] = options.labelledBy
      break
  }

  // List/grid attributes
  if (options.level) props['aria-level'] = options.level
  if (options.setSize) props['aria-setsize'] = options.setSize
  if (options.posInSet) props['aria-posinset'] = options.posInSet

  return props
}

// Color contrast utilities
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16) / 255
    const g = parseInt(hex.substr(2, 2), 16) / 255
    const b = parseInt(hex.substr(4, 2), 16) / 255

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2]
  }

  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)

  return (brightest + 0.05) / (darkest + 0.05)
}

export const meetsWCAGStandard = (
  color1: string, 
  color2: string, 
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean => {
  const ratio = getContrastRatio(color1, color2)
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7
  } else {
    return size === 'large' ? ratio >= 3 : ratio >= 4.5
  }
}

// Reduced motion utilities
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

export const useReducedMotion = () => {
  const [shouldReduceMotion, setShouldReduceMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setShouldReduceMotion(mediaQuery.matches)

    const handleChange = () => setShouldReduceMotion(mediaQuery.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return shouldReduceMotion
}

// Skip link utilities
export const SkipLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => {
  return (
    <a
      href={href}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:shadow-lg"
    >
      {children}
    </a>
  )
}

// Screen reader only text
export const ScreenReaderOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <span className="sr-only">{children}</span>
}
