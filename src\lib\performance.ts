import { useEffect, useRef, useState, useCallback } from 'react'

// Lazy loading utilities
export const useLazyLoad = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [threshold])

  return [ref, isVisible] as const
}

// Debounced value hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttled callback hook
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// Virtual scrolling hook
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0)
  
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  )
  
  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    startIndex,
    endIndex
  }
}

// Image optimization utilities
export const useImagePreload = (src: string) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const img = new Image()
    
    img.onload = () => setIsLoaded(true)
    img.onerror = () => setError('Failed to load image')
    
    img.src = src

    return () => {
      img.onload = null
      img.onerror = null
    }
  }, [src])

  return { isLoaded, error }
}

// Memory management utilities
export const useMemoryOptimization = () => {
  const cleanup = useCallback(() => {
    // Force garbage collection if available (development only)
    if (typeof window !== 'undefined' && 'gc' in window) {
      (window as any).gc()
    }
  }, [])

  const measureMemory = useCallback(async () => {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }, [])

  return { cleanup, measureMemory }
}

// Bundle size optimization utilities
export const loadChunk = async (chunkName: string) => {
  try {
    const module = await import(/* webpackChunkName: "[request]" */ `../chunks/${chunkName}`)
    return module.default || module
  } catch (error) {
    console.error(`Failed to load chunk: ${chunkName}`, error)
    throw error
  }
}

// Performance monitoring
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<{
    renderTime: number
    memoryUsage: number | null
    fps: number
  }>({
    renderTime: 0,
    memoryUsage: null,
    fps: 0
  })

  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        
        setMetrics(prev => ({
          ...prev,
          fps,
          memoryUsage: (performance as any).memory?.usedJSHeapSize || null
        }))
        
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(measureFPS)
    }

    animationId = requestAnimationFrame(measureFPS)

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [])

  const measureRenderTime = useCallback((componentName: string) => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      setMetrics(prev => ({ ...prev, renderTime }))
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`)
      }
    }
  }, [])

  return { metrics, measureRenderTime }
}

// Cache utilities
export const createCache = <K, V>(maxSize: number = 100) => {
  const cache = new Map<K, V>()
  
  const get = (key: K): V | undefined => {
    const value = cache.get(key)
    if (value !== undefined) {
      // Move to end (LRU)
      cache.delete(key)
      cache.set(key, value)
    }
    return value
  }
  
  const set = (key: K, value: V): void => {
    if (cache.has(key)) {
      cache.delete(key)
    } else if (cache.size >= maxSize) {
      // Remove oldest entry
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }
    cache.set(key, value)
  }
  
  const clear = (): void => {
    cache.clear()
  }
  
  const size = (): number => cache.size
  
  return { get, set, clear, size }
}

// Web Workers utilities
export const createWebWorker = (workerFunction: Function) => {
  const blob = new Blob([`(${workerFunction.toString()})()`], {
    type: 'application/javascript'
  })
  
  return new Worker(URL.createObjectURL(blob))
}

// Service Worker utilities
export const registerServiceWorker = async (swPath: string = '/sw.js') => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register(swPath)
      console.log('Service Worker registered successfully:', registration)
      return registration
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      throw error
    }
  } else {
    throw new Error('Service Workers not supported')
  }
}

// Critical resource hints
export const addResourceHints = (resources: Array<{
  href: string
  as?: string
  type?: 'preload' | 'prefetch' | 'preconnect' | 'dns-prefetch'
}>) => {
  resources.forEach(({ href, as, type = 'preload' }) => {
    const link = document.createElement('link')
    link.rel = type
    link.href = href
    if (as) link.as = as
    document.head.appendChild(link)
  })
}

// Bundle analysis utilities
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    
    console.group('Bundle Analysis')
    console.log('Scripts:', scripts.length)
    console.log('Stylesheets:', styles.length)
    console.groupEnd()
  }
}
