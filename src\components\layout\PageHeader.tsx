import React from 'react'
import { cn } from '@/src/lib/utils'

interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
  actions?: React.ReactNode
}

export const PageHeader: React.FC<PageHeaderProps> = ({ 
  title, 
  description, 
  children, 
  className,
  actions 
}) => {
  return (
    <div className={cn(
      "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",
      className
    )}>
      <div className="flex-1 min-w-0">
        <h1 className="heading-2 text-foreground">{title}</h1>
        {description && (
          <p className="body-default text-muted-foreground mt-1">{description}</p>
        )}
        {children}
      </div>
      {actions && (
        <div className="flex-shrink-0">
          {actions}
        </div>
      )}
    </div>
  )
}

interface SectionHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
  actions?: React.ReactNode
  level?: 2 | 3 | 4 | 5 | 6
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({ 
  title, 
  description, 
  children, 
  className,
  actions,
  level = 3
}) => {
  const headingClasses = {
    2: "heading-2",
    3: "heading-3", 
    4: "heading-4",
    5: "heading-5",
    6: "heading-6"
  }

  return (
    <div className={cn(
      "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4",
      className
    )}>
      <div className="flex-1 min-w-0">
        <h2 className={cn(headingClasses[level], "text-foreground")}>{title}</h2>
        {description && (
          <p className="body-small text-muted-foreground mt-1">{description}</p>
        )}
        {children}
      </div>
      {actions && (
        <div className="flex-shrink-0">
          {actions}
        </div>
      )}
    </div>
  )
}

interface StatsHeaderProps {
  stats: Array<{
    label: string
    value: string | number
    change?: {
      value: string
      trend: 'up' | 'down' | 'neutral'
    }
    color?: 'primary' | 'success' | 'warning' | 'destructive' | 'info'
  }>
  className?: string
}

export const StatsHeader: React.FC<StatsHeaderProps> = ({ stats, className }) => {
  const colorClasses = {
    primary: "text-primary",
    success: "text-success",
    warning: "text-warning", 
    destructive: "text-destructive",
    info: "text-info"
  }

  const trendClasses = {
    up: "text-success",
    down: "text-destructive",
    neutral: "text-muted-foreground"
  }

  const trendIcons = {
    up: "↗",
    down: "↘", 
    neutral: "→"
  }

  return (
    <div className={cn(
      "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",
      className
    )}>
      {stats.map((stat, index) => (
        <div key={index} className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="caption">{stat.label}</p>
              <p className={cn(
                "text-2xl font-bold mt-1",
                stat.color ? colorClasses[stat.color] : "text-foreground"
              )}>
                {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
              </p>
              {stat.change && (
                <div className={cn(
                  "flex items-center mt-1 text-xs",
                  trendClasses[stat.change.trend]
                )}>
                  <span className="mr-1">{trendIcons[stat.change.trend]}</span>
                  <span>{stat.change.value}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

interface BreadcrumbProps {
  items: Array<{
    label: string
    href?: string
    onClick?: () => void
  }>
  className?: string
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className }) => {
  return (
    <nav className={cn("flex items-center space-x-2 text-sm mb-4", className)}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="text-muted-foreground">/</span>
          )}
          {item.href || item.onClick ? (
            <button
              onClick={item.onClick}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {item.label}
            </button>
          ) : (
            <span className="text-foreground font-medium">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

interface TabsHeaderProps {
  tabs: Array<{
    id: string
    label: string
    count?: number
  }>
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export const TabsHeader: React.FC<TabsHeaderProps> = ({ 
  tabs, 
  activeTab, 
  onTabChange, 
  className 
}) => {
  return (
    <div className={cn(
      "flex space-x-1 bg-muted p-1 rounded-lg mb-6",
      className
    )}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={cn(
            "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            activeTab === tab.id
              ? "bg-background text-foreground shadow-sm"
              : "text-muted-foreground hover:text-foreground hover:bg-background/50"
          )}
        >
          <span>{tab.label}</span>
          {tab.count !== undefined && (
            <span className={cn(
              "ml-2 px-2 py-0.5 text-xs rounded-full",
              activeTab === tab.id
                ? "bg-muted text-muted-foreground"
                : "bg-muted/50 text-muted-foreground"
            )}>
              {tab.count}
            </span>
          )}
        </button>
      ))}
    </div>
  )
}
