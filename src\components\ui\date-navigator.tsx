import React from 'react'
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react'
import { cn } from '@/src/lib/utils'
import { Button } from './button'

interface DateNavigatorProps {
  selectedDate: Date
  onDateChange: (days: number) => void
  onDateSelect?: (date: Date) => void
  locale?: string
  className?: string
  showCalendar?: boolean
  disabled?: boolean
  t: (key: string) => string
}

export const DateNavigator: React.FC<DateNavigatorProps> = ({
  selectedDate,
  onDateChange,
  onDateSelect,
  locale = 'en-US',
  className,
  showCalendar = false,
  disabled = false,
  t
}) => {
  const isToday = (date: Date): boolean => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isFuture = (date: Date): boolean => {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return date > today
  }

  const displayDate = isToday(selectedDate) 
    ? t('general.today') 
    : selectedDate.toLocaleDateString(locale, { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      })

  const formatDateForInput = (date: Date): string => {
    return date.toISOString().split('T')[0]
  }

  const handleDateInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onDateSelect) {
      const newDate = new Date(event.target.value)
      onDateSelect(newDate)
    }
  }

  return (
    <div className={cn(
      "flex items-center space-x-1 bg-card border border-border rounded-lg p-1",
      "shadow-sm",
      className
    )}>
      <Button
        variant="ghost"
        size="icon-sm"
        onClick={() => onDateChange(-1)}
        disabled={disabled}
        className="h-8 w-8 flex-shrink-0"
        aria-label="Previous day"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      <div className="flex-1 min-w-0 px-2">
        {showCalendar && onDateSelect ? (
          <div className="relative">
            <input
              type="date"
              value={formatDateForInput(selectedDate)}
              onChange={handleDateInputChange}
              disabled={disabled}
              className="w-full bg-transparent text-center text-sm font-medium border-none outline-none cursor-pointer"
              aria-label="Select date"
            />
            <Calendar className="absolute right-0 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
          </div>
        ) : (
          <span className="block text-center text-sm font-medium text-foreground">
            {displayDate}
          </span>
        )}
      </div>

      <Button
        variant="ghost"
        size="icon-sm"
        onClick={() => onDateChange(1)}
        disabled={disabled || isFuture(new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000))}
        className="h-8 w-8 flex-shrink-0"
        aria-label="Next day"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  )
}

interface WeekNavigatorProps {
  selectedDate: Date
  onDateChange: (date: Date) => void
  locale?: string
  className?: string
  disabled?: boolean
  t: (key: string) => string
}

export const WeekNavigator: React.FC<WeekNavigatorProps> = ({
  selectedDate,
  onDateChange,
  locale = 'en-US',
  className,
  disabled = false,
  t
}) => {
  const getWeekDays = (date: Date): Date[] => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const isToday = (date: Date): boolean => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isSelected = (date: Date): boolean => {
    return date.toDateString() === selectedDate.toDateString()
  }

  const weekDays = getWeekDays(selectedDate)

  return (
    <div className={cn(
      "grid grid-cols-7 gap-1 bg-card border border-border rounded-lg p-2",
      "shadow-sm",
      className
    )}>
      {weekDays.map((day, index) => (
        <button
          key={index}
          onClick={() => onDateChange(day)}
          disabled={disabled}
          className={cn(
            "flex flex-col items-center p-2 rounded-md text-xs transition-colors",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            isSelected(day)
              ? "bg-primary text-primary-foreground"
              : isToday(day)
              ? "bg-accent text-accent-foreground font-medium"
              : "text-muted-foreground hover:text-foreground hover:bg-accent"
          )}
        >
          <span className="font-medium">
            {day.toLocaleDateString(locale, { weekday: 'short' })}
          </span>
          <span className="text-lg font-bold mt-1">
            {day.getDate()}
          </span>
        </button>
      ))}
    </div>
  )
}

interface MonthNavigatorProps {
  selectedDate: Date
  onDateChange: (date: Date) => void
  locale?: string
  className?: string
  disabled?: boolean
  t: (key: string) => string
}

export const MonthNavigator: React.FC<MonthNavigatorProps> = ({
  selectedDate,
  onDateChange,
  locale = 'en-US',
  className,
  disabled = false,
  t
}) => {
  const changeMonth = (direction: number) => {
    const newDate = new Date(selectedDate)
    newDate.setMonth(newDate.getMonth() + direction)
    onDateChange(newDate)
  }

  const monthYear = selectedDate.toLocaleDateString(locale, { 
    year: 'numeric', 
    month: 'long' 
  })

  return (
    <div className={cn(
      "flex items-center justify-between bg-card border border-border rounded-lg p-3",
      "shadow-sm",
      className
    )}>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => changeMonth(-1)}
        disabled={disabled}
        aria-label="Previous month"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      <h3 className="heading-5 text-center flex-1">
        {monthYear}
      </h3>

      <Button
        variant="ghost"
        size="icon"
        onClick={() => changeMonth(1)}
        disabled={disabled}
        aria-label="Next month"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  )
}
