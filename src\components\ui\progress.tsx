import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/src/lib/utils"

const progressVariants = cva(
  "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
  {
    variants: {
      size: {
        sm: "h-2",
        default: "h-4",
        lg: "h-6",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 bg-primary transition-all",
  {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-success",
        warning: "bg-warning",
        destructive: "bg-destructive",
        info: "bg-info",
        gradient: "bg-gradient-to-r from-primary to-primary/80",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  variant?: "default" | "success" | "warning" | "destructive" | "info" | "gradient"
  showValue?: boolean
  formatValue?: (value: number) => string
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, size, variant = "default", showValue = false, formatValue, ...props }, ref) => {
  const displayValue = value || 0
  const formattedValue = formatValue ? formatValue(displayValue) : `${Math.round(displayValue)}%`

  return (
    <div className="w-full">
      {showValue && (
        <div className="flex justify-between text-sm mb-1">
          <span className="text-muted-foreground">Progress</span>
          <span className="font-medium">{formattedValue}</span>
        </div>
      )}
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(progressVariants({ size }), className)}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(progressIndicatorVariants({ variant }))}
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
    </div>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

// Circular progress component
const CircularProgress = React.forwardRef<
  SVGSVGElement,
  React.SVGProps<SVGSVGElement> & {
    value?: number
    size?: number
    strokeWidth?: number
    variant?: "default" | "success" | "warning" | "destructive" | "info"
    showValue?: boolean
    formatValue?: (value: number) => string
  }
>(({ 
  className, 
  value = 0, 
  size = 120, 
  strokeWidth = 8, 
  variant = "default",
  showValue = false,
  formatValue,
  ...props 
}, ref) => {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (value / 100) * circumference

  const colors = {
    default: "hsl(var(--primary))",
    success: "hsl(var(--success))",
    warning: "hsl(var(--warning))",
    destructive: "hsl(var(--destructive))",
    info: "hsl(var(--info))",
  }

  const formattedValue = formatValue ? formatValue(value) : `${Math.round(value)}%`

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg
        ref={ref}
        className={cn("transform -rotate-90", className)}
        width={size}
        height={size}
        {...props}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="hsl(var(--muted))"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="opacity-20"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={colors[variant]}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium">{formattedValue}</span>
        </div>
      )}
    </div>
  )
})
CircularProgress.displayName = "CircularProgress"

// Multi-segment progress bar for calorie tracking
const CalorieProgress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    consumed: number
    burned: number
    goal: number
    size?: "sm" | "default" | "lg"
  }
>(({ className, consumed, burned, goal, size = "default", ...props }, ref) => {
  const net = consumed - burned
  const consumedPercentage = Math.min((consumed / goal) * 100, 100)
  const burnedPercentage = Math.min((burned / goal) * 100, 100)
  const netPercentage = Math.min((net / goal) * 100, 100)

  const heights = {
    sm: "h-2",
    default: "h-4",
    lg: "h-6",
  }

  return (
    <div ref={ref} className={cn("w-full space-y-2", className)} {...props}>
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>Consumed: {consumed.toLocaleString()}</span>
        <span>Burned: {burned.toLocaleString()}</span>
        <span>Net: {net.toLocaleString()}</span>
      </div>
      
      <div className={cn("relative w-full overflow-hidden rounded-full bg-secondary", heights[size])}>
        {/* Consumed calories */}
        <div
          className="absolute left-0 top-0 h-full bg-calories-consumed transition-all duration-300"
          style={{ width: `${consumedPercentage}%` }}
        />
        
        {/* Burned calories (overlay) */}
        <div
          className="absolute left-0 top-0 h-full bg-calories-burned/60 transition-all duration-300"
          style={{ width: `${burnedPercentage}%` }}
        />
        
        {/* Goal marker */}
        <div className="absolute top-0 h-full w-0.5 bg-foreground/20" style={{ left: "100%" }} />
      </div>
      
      <div className="flex justify-between text-xs">
        <span className="text-calories-consumed">Consumed</span>
        <span className="text-calories-burned">Burned</span>
        <span className="text-muted-foreground">Goal: {goal.toLocaleString()}</span>
      </div>
    </div>
  )
})
CalorieProgress.displayName = "CalorieProgress"

export { Progress, CircularProgress, CalorieProgress }
