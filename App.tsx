import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { useAppState } from './hooks/useAppState';
import type { ActiveView, FoodEntry, ExerciseEntry, DailyLog, ChatMessage, AppState, FoodAnalysis, UserProfile } from './types';
import { getAiAdvice, getAiFoodAnalysis } from './services/geminiService';
import { HomeIcon, ClipboardIcon, SparklesIcon, TrashIcon, SendIcon, SettingsIcon, CameraIcon, ChevronLeftIcon, ChevronRightIcon, UserCircleIcon, DownloadIcon, UploadIcon } from './components/Icons';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  Button,
  Input,
  Label,
  SimpleSelect as Select,
  Progress,
  CalorieProgress,
  Modal,
  LoadingSpinner,
  LoadingState,
  DateNavigator as EnhancedDateNavigator,
  FadeIn,
  StaggeredList,
  CountUp,
  ScaleIn
} from './src/components/ui';
import {
  AppLayout,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  MainContent,
  MobileHeader,
  ContentArea,
  MobileNavigation,
  Navigation,
  NavigationItem,
  MobileNavigationItem
} from './src/components/layout/AppLayout';
import { PageHeader, SectionHeader, TabsHeader } from './src/components/layout/PageHeader';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';
import { useTranslation } from './hooks/useTranslation';
import { cn, formatCalories, getCalorieStatus } from './src/lib/utils';
import { SkipLink, ScreenReaderOnly, announceToScreenReader } from './src/lib/accessibility';

// --- Helper Functions ---
const getDateString = (date: Date): string => date.toISOString().split('T')[0];
const isToday = (date: Date): boolean => getDateString(date) === getDateString(new Date());

// --- Recharts Pie Chart Label ---
const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

// --- Camera Component ---
const CameraView = ({ onClose, onScan, isScanning, t }: { onClose: () => void, onScan: (data: string) => Promise<void>, isScanning: boolean, t: (key: string) => string }) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [error, setError] = useState('');
    const uploadInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        let stream: MediaStream;
        const startCamera = async () => {
            try {
                if(navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                    if(videoRef.current) {
                        videoRef.current.srcObject = stream;
                    }
                }
            } catch (err) {
                console.error("Error accessing camera:", err);
                setError(t('camera.permission_error'));
            }
        };

        startCamera();
        
        return () => {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        };
    }, [t]);

    const handleCapture = () => {
        if (videoRef.current) {
            const canvas = document.createElement('canvas');
            canvas.width = videoRef.current.videoWidth;
            canvas.height = videoRef.current.videoHeight;
            canvas.getContext('2d')?.drawImage(videoRef.current, 0, 0);
            const dataUrl = canvas.toDataURL('image/jpeg');
            onScan(dataUrl.split(',')[1]);
        }
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const base64 = (e.target?.result as string)?.split(',')[1];
                if (base64) {
                    onScan(base64);
                }
            };
            reader.readAsDataURL(file);
        }
    };
    
    if (error) {
        return <p className="text-red-400 text-center">{error}</p>
    }

    return (
        <div className="flex flex-col items-center">
            <div className="w-full bg-black rounded-lg overflow-hidden mb-4 relative">
                <video ref={videoRef} autoPlay playsInline className="w-full h-auto"></video>
                {isScanning && (
                     <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center text-white">
                        <div className="w-8 h-8 border-4 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
                        <p className="mt-2">{t('camera.scanning')}</p>
                    </div>
                )}
            </div>
            <div className="grid grid-cols-2 gap-2 w-full">
                <Button onClick={handleCapture} disabled={isScanning}>
                    <CameraIcon className="w-5 h-5 mr-2" />
                    {t('log.scan_food')}
                </Button>
                <Button onClick={() => uploadInputRef.current?.click()} disabled={isScanning} className="bg-green-600 hover:bg-green-700">
                    <UploadIcon className="w-5 h-5 mr-2" />
                    {t('log.upload_image')}
                </Button>
                <input type="file" accept="image/*" ref={uploadInputRef} onChange={handleFileUpload} className="hidden" />
            </div>
        </div>
    );
};

// Use the enhanced DateNavigator component
const DateNavigatorWrapper = ({ selectedDate, changeDate, t, locale }: { selectedDate: Date; changeDate: (days: number) => void; t: (key: string) => string; locale: string; }) => {
    return (
        <EnhancedDateNavigator
            selectedDate={selectedDate}
            onDateChange={changeDate}
            locale={locale}
            t={t}
            showCalendar={false}
        />
    );
}

// --- View Components ---

const DashboardView = ({ dailyGoal, logs, selectedDate, setSelectedDate, t, locale }: { dailyGoal: number; logs: Record<string, DailyLog>; selectedDate: Date, setSelectedDate: (d: Date) => void, t: (key: string) => string, locale: string }) => {
    const currentLog = logs[getDateString(selectedDate)] || { food: [], exercise: [] };
    const intake = currentLog.food.reduce((sum, item) => sum + item.calories, 0);
    const burned = currentLog.exercise.reduce((sum, item) => sum + item.calories, 0);
    const net = intake - burned;

    const pieData = [
        { name: t('dashboard.consumed'), value: intake > dailyGoal ? dailyGoal : intake },
        { name: t('dashboard.remaining'), value: intake > dailyGoal ? 0 : dailyGoal - intake },
    ];


    const last7DaysData = useMemo(() => {
        return Array.from({ length: 7 }).map((_, i) => {
            const d = new Date();
            d.setDate(d.getDate() - i);
            const dateStr = getDateString(d);
            const log = logs[dateStr] || { food: [], exercise: [] };
            const dayIntake = log.food.reduce((s, item) => s + item.calories, 0);
            const dayBurned = log.exercise.reduce((s, item) => s + item.calories, 0);
            return {
                name: d.toLocaleDateString(locale, { month: '2-digit', day: '2-digit' }),
                [t('dashboard.net_calories')]: dayIntake - dayBurned,
                date: dateStr
            };
        }).reverse();
    }, [logs, t, locale]);

    const changeDate = (days: number) => {
        const newDate = new Date(selectedDate);
        newDate.setDate(newDate.getDate() + days);
        setSelectedDate(newDate);
    }
    
    const calorieStatus = getCalorieStatus(intake, dailyGoal);

    return (
        <FadeIn className="container-padding section-spacing content-spacing">
            <PageHeader
                title={t('dashboard.title')}
                actions={
                    <DateNavigatorWrapper selectedDate={selectedDate} changeDate={changeDate} t={t} locale={locale} />
                }
            />

            {/* Calorie Progress Overview */}
            <Card variant="elevated" className="mb-6">
                <CardHeader>
                    <CardTitle>{t('dashboard.goal_progress')}</CardTitle>
                    <CardDescription>
                        {formatCalories(Math.max(0, dailyGoal - intake))} {t('dashboard.remaining')} of {formatCalories(dailyGoal)}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <CalorieProgress
                        consumed={intake}
                        burned={burned}
                        goal={dailyGoal}
                        size="lg"
                    />
                </CardContent>
            </Card>

            {/* Stats Cards */}
            <StaggeredList className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6" staggerDelay={150}>
                <Card variant="interactive">
                    <CardHeader className="text-center">
                        <CardDescription>{t('dashboard.intake')}</CardDescription>
                        <CardTitle className="text-calories-consumed text-3xl">
                            <CountUp value={intake} formatter={(v) => formatCalories(v)} />
                        </CardTitle>
                    </CardHeader>
                </Card>
                <Card variant="interactive">
                    <CardHeader className="text-center">
                        <CardDescription>{t('dashboard.burned')}</CardDescription>
                        <CardTitle className="text-calories-burned text-3xl">
                            <CountUp value={burned} formatter={(v) => formatCalories(v)} />
                        </CardTitle>
                    </CardHeader>
                </Card>
                <Card variant="interactive">
                    <CardHeader className="text-center">
                        <CardDescription>{t('dashboard.net')}</CardDescription>
                        <CardTitle className={cn(
                            "text-3xl",
                            net > dailyGoal ? "text-calories-excess" : "text-calories-remaining"
                        )}>
                            <CountUp value={net} formatter={(v) => formatCalories(v)} />
                        </CardTitle>
                    </CardHeader>
                </Card>
            </StaggeredList>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <Card variant="elevated" className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle>{t('dashboard.goal_progress')}</CardTitle>
                        <CardDescription>Daily calorie breakdown</CardDescription>
                    </CardHeader>
                    <CardContent className="h-64">
                         <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                                <Pie
                                    data={pieData}
                                    dataKey="value"
                                    nameKey="name"
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={80}
                                    fill="hsl(var(--chart-1))"
                                    labelLine={false}
                                    label={renderCustomizedLabel}
                                >
                                    {pieData.map((_, index) => (
                                        <Cell key={`cell-${index}`} fill={index === 0 ? 'hsl(var(--chart-1))' : 'hsl(var(--muted))'} />
                                    ))}
                                </Pie>
                                <Tooltip
                                    contentStyle={{
                                        backgroundColor: 'hsl(var(--popover))',
                                        border: '1px solid hsl(var(--border))',
                                        borderRadius: '0.5rem',
                                        color: 'hsl(var(--popover-foreground))'
                                    }}
                                />
                                <Legend />
                            </PieChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>

                <Card variant="elevated" className="lg:col-span-3">
                    <CardHeader>
                        <CardTitle>{t('dashboard.net_calories_7_days')}</CardTitle>
                        <CardDescription>Weekly calorie trend</CardDescription>
                    </CardHeader>
                    <CardContent className="h-80">
                         <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={last7DaysData}>
                                <XAxis
                                    dataKey="name"
                                    stroke="hsl(var(--muted-foreground))"
                                    fontSize={12}
                                    tickLine={false}
                                    axisLine={false}
                                />
                                <YAxis
                                    stroke="hsl(var(--muted-foreground))"
                                    fontSize={12}
                                    tickLine={false}
                                    axisLine={false}
                                />
                                <Tooltip
                                    cursor={{fill: 'hsl(var(--muted) / 0.5)'}}
                                    contentStyle={{
                                        backgroundColor: 'hsl(var(--popover))',
                                        border: '1px solid hsl(var(--border))',
                                        borderRadius: '0.5rem',
                                        color: 'hsl(var(--popover-foreground))'
                                    }}
                                />
                                <Bar
                                    dataKey={t('dashboard.net_calories')}
                                    fill="hsl(var(--chart-1))"
                                    radius={[4, 4, 0, 0]}
                                />
                            </BarChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>
            </div>
        </FadeIn>
    );
};

const LogView = ({ currentLog, addFood, addExercise, removeFood, removeExercise, appState, t, setSelectedDate, selectedDate, locale }: { currentLog: DailyLog; addFood: (f: Omit<FoodEntry, 'id'>) => void; addExercise: (e: Omit<ExerciseEntry, 'id'>) => void; removeFood: (id: string) => void; removeExercise: (id: string) => void; appState: AppState, t: (key: string) => string, setSelectedDate: (d: Date) => void, selectedDate: Date; locale: string; }) => {
    const [activeTab, setActiveTab] = useState('food');
    const [isCameraOpen, setIsCameraOpen] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const [scanError, setScanError] = useState('');
    const [scannedFood, setScannedFood] = useState<{name: string, calories: string}>({name: '', calories: ''});
    const fileUploadRef = useRef<HTMLInputElement>(null);

    const changeDate = (days: number) => {
        const newDate = new Date(selectedDate);
        newDate.setDate(newDate.getDate() + days);
        setSelectedDate(newDate);
    }
    
    const handleScan = async (base64Image: string) => {
        if (!appState.apiKey) {
            setScanError(t('settings.api_key_required_error'));
            setIsCameraOpen(false);
            return;
        }
        setIsScanning(true);
        setScanError('');
        try {
            const analysis: FoodAnalysis = await getAiFoodAnalysis(base64Image, appState.apiKey, appState.aiModel, appState.language);
            
            if (analysis.foodName === 'UNIDENTIFIED') {
                setScannedFood({name: '', calories: ''});
                setScanError(t('camera.unidentified'));
            } else {
                setScannedFood({
                    name: analysis.foodName,
                    calories: analysis.calories > 0 ? String(analysis.calories) : ''
                });
                setIsCameraOpen(false);
            }
        } catch (error) {
            console.error(error);
            setScanError(t('camera.scan_error'));
        } finally {
            setIsScanning(false);
        }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (loadEvent) => {
                const base64String = (loadEvent.target?.result as string).split(',')[1];
                handleScan(base64String);
            };
            reader.readAsDataURL(file);
        }
     };

    const FoodForm = () => {
        const [name, setName] = useState(scannedFood.name);
        const [calories, setCalories] = useState(scannedFood.calories);
        const [meal, setMeal] = useState<'breakfast' | 'lunch' | 'dinner' | 'snack'>('snack');
        
        useEffect(() => {
            setName(scannedFood.name);
            setCalories(scannedFood.calories);
        }, [scannedFood]);
        
        const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            if (name && calories) {
                addFood({ name, calories: parseInt(calories), meal });
                setName('');
                setCalories('');
                setScannedFood({name: '', calories: ''});
            }
        };
        return (
             <Card as="form" onSubmit={handleSubmit} variant="elevated">
                <CardHeader>
                    <CardTitle>{t('log.log_food')}</CardTitle>
                    <CardDescription>Add food items to track your calorie intake</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="food-name">{t('log.food_name')}</Label>
                            <Input
                                id="food-name"
                                type="text"
                                value={name}
                                onChange={e => setName(e.target.value)}
                                placeholder={t('log.food_name')}
                                required
                            />
                        </div>
                        <div>
                            <Label htmlFor="food-calories">{t('log.calories')}</Label>
                            <Input
                                id="food-calories"
                                type="number"
                                value={calories}
                                onChange={e => setCalories(e.target.value)}
                                placeholder={t('log.calories')}
                                required
                            />
                        </div>
                        <div>
                            <Label htmlFor="meal-type">Meal Type</Label>
                            <Select id="meal-type" value={meal} onChange={e => setMeal(e.target.value as any)}>
                                <option value="breakfast">{t('log.meal.breakfast')}</option>
                                <option value="lunch">{t('log.meal.lunch')}</option>
                                <option value="dinner">{t('log.meal.dinner')}</option>
                                <option value="snack">{t('log.meal.snack')}</option>
                            </Select>
                        </div>
                    </div>

                    <div className="space-y-3">
                        <Button type="submit" className="w-full" size="lg">
                            {t('log.add_food')}
                        </Button>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <Button
                                type="button"
                                onClick={() => setIsCameraOpen(true)}
                                variant="outline"
                                leftIcon={<CameraIcon className="w-4 h-4" aria-hidden="true" />}
                                aria-label="Scan food with camera"
                            >
                                {t('log.scan_food')}
                            </Button>
                            <Button
                                type="button"
                                onClick={() => fileUploadRef.current?.click()}
                                variant="outline"
                                leftIcon={<UploadIcon className="w-4 h-4" aria-hidden="true" />}
                                aria-label="Upload food image"
                            >
                                {t('log.upload_image')}
                            </Button>
                        </div>
                        <input type="file" ref={fileUploadRef} onChange={handleFileSelect} accept="image/*" className="hidden" />
                    </div>
                </CardContent>
            </Card>
        );
    }
    
    const ExerciseForm = () => {
        const [name, setName] = useState('');
        const [duration, setDuration] = useState('');
        const [calories, setCalories] = useState('');
        const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            if (name && duration && calories) {
                addExercise({ name, duration: parseInt(duration), calories: parseInt(calories) });
                setName('');
                setDuration('');
                setCalories('');
            }
        }
        return (
            <Card as="form" onSubmit={handleSubmit} variant="elevated">
                <CardHeader>
                    <CardTitle>{t('log.log_exercise')}</CardTitle>
                    <CardDescription>Track your physical activities and calories burned</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="exercise-name">{t('log.exercise_name')}</Label>
                            <Input
                                id="exercise-name"
                                type="text"
                                value={name}
                                onChange={e => setName(e.target.value)}
                                placeholder={t('log.exercise_name')}
                                required
                            />
                        </div>
                        <div>
                            <Label htmlFor="exercise-duration">{t('log.duration_mins')}</Label>
                            <Input
                                id="exercise-duration"
                                type="number"
                                value={duration}
                                onChange={e => setDuration(e.target.value)}
                                placeholder={t('log.duration_mins')}
                                required
                            />
                        </div>
                        <div>
                            <Label htmlFor="exercise-calories">{t('log.calories_burned')}</Label>
                            <Input
                                id="exercise-calories"
                                type="number"
                                value={calories}
                                onChange={e => setCalories(e.target.value)}
                                placeholder={t('log.calories_burned')}
                                required
                            />
                        </div>
                    </div>

                    <Button type="submit" variant="success" className="w-full" size="lg">
                        {t('log.add_exercise')}
                    </Button>
                </CardContent>
            </Card>
        );
    }
    
    const tabs = [
        { id: 'food', label: t('log.food'), count: currentLog.food.length },
        { id: 'exercise', label: t('log.exercise'), count: currentLog.exercise.length }
    ];

    return (
        <FadeIn className="container-padding section-spacing content-spacing">
            <PageHeader
                title={isToday(selectedDate) ? t('log.todays_log') : selectedDate.toLocaleDateString(locale, { month: 'short', day: 'numeric' }) + t('log.date_log')}
                actions={
                    <DateNavigatorWrapper selectedDate={selectedDate} changeDate={changeDate} t={t} locale={locale} />
                }
            />

            <TabsHeader
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    {activeTab === 'food' ? <FoodForm /> : <ExerciseForm />}
                    {scanError && <p className="text-red-400 text-center mt-2">{scanError}</p>}
                </div>

                <Card variant="elevated">
                    <CardHeader>
                        <CardTitle>
                            {activeTab === 'food' ? t('log.food_entries') : t('log.exercise_entries')}
                        </CardTitle>
                        <CardDescription>
                            {activeTab === 'food'
                                ? `${currentLog.food.length} food items logged`
                                : `${currentLog.exercise.length} exercises logged`
                            }
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                            {activeTab === 'food' && currentLog.food.map(f => (
                                <div key={f.id} className="flex justify-between items-center bg-card/50 border border-border/50 p-4 rounded-lg group hover:bg-accent/50 transition-colors">
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2 mb-1">
                                            <span className="text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground capitalize">
                                                {t(`log.meal.${f.meal}`)}
                                            </span>
                                        </div>
                                        <h4 className="font-medium text-foreground">{f.name}</h4>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <span className="text-calories-consumed font-semibold">
                                            {f.calories} {t('dashboard.kcal')}
                                        </span>
                                        <Button
                                            size="icon-sm"
                                            variant="ghost"
                                            onClick={() => removeFood(f.id)}
                                            className="text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-all"
                                            aria-label={`Remove ${f.name} from food log`}
                                        >
                                            <TrashIcon className="w-4 h-4" aria-hidden="true"/>
                                        </Button>
                                    </div>
                                </div>
                            ))}
                            {activeTab === 'exercise' && currentLog.exercise.map(e => (
                                <div key={e.id} className="flex justify-between items-center bg-card/50 border border-border/50 p-4 rounded-lg group hover:bg-accent/50 transition-colors">
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-foreground">{e.name}</h4>
                                        <p className="text-sm text-muted-foreground">
                                            {e.duration} {t('log.duration_unit')}
                                        </p>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <span className="text-calories-burned font-semibold">
                                            -{e.calories} {t('dashboard.kcal')}
                                        </span>
                                        <Button
                                            size="icon-sm"
                                            variant="ghost"
                                            onClick={() => removeExercise(e.id)}
                                            className="text-muted-foreground hover:text-destructive opacity-0 group-hover:opacity-100 transition-all"
                                            aria-label={`Remove ${e.name} from exercise log`}
                                        >
                                            <TrashIcon className="w-4 h-4" aria-hidden="true"/>
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                        {activeTab === 'food' && currentLog.food.length === 0 && (
                            <div className="text-center py-12">
                                <div className="text-4xl mb-4">🍽️</div>
                                <p className="text-muted-foreground">{t('log.no_food')}</p>
                            </div>
                        )}
                        {activeTab === 'exercise' && currentLog.exercise.length === 0 && (
                            <div className="text-center py-12">
                                <div className="text-4xl mb-4">🏃‍♂️</div>
                                <p className="text-muted-foreground">{t('log.no_exercise')}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
             <Modal isOpen={isCameraOpen} onClose={() => setIsCameraOpen(false)} title={t('camera.modal_title')}>
                <CameraView onClose={() => setIsCameraOpen(false)} onScan={handleScan} isScanning={isScanning} t={t} />
                {scanError && <p className="text-red-400 text-center mt-2">{scanError}</p>}
            </Modal>
        </FadeIn>
    );
};

const AiAssistantView = ({ appState, setChatHistory, currentLog, onNav, t }: { appState: AppState, setChatHistory: (history: ChatMessage[]) => void, currentLog: DailyLog; onNav: (view: ActiveView) => void; t: (key: string) => string; }) => {
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const endOfMessagesRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [appState.chatHistory]);

    const handleSend = async () => {
        if (input.trim() === '' || isLoading || !appState.apiKey) return;
        
        const userMessage: ChatMessage = { role: 'user', text: input };
        const newHistory = [...appState.chatHistory, userMessage];
        setChatHistory(newHistory);
        
        const currentInput = input;
        setInput('');
        setIsLoading(true);

        const aiResponse = await getAiAdvice(currentInput, appState.chatHistory, currentLog, appState.dailyGoal, appState.userProfile, appState.apiKey, appState.aiModel, appState.language);
        const modelMessage: ChatMessage = { role: 'model', text: aiResponse };
        
        setChatHistory([...newHistory, modelMessage]);
        setIsLoading(false);
    };
    
    if (!appState.apiKey) {
        return (
            <div className="p-4 md:p-6 animate-fade-in text-center flex flex-col items-center justify-center h-full">
                <Card className="max-w-md">
                    <CardHeader>
                        <CardTitle>{t('ai.not_configured')}</CardTitle>
                        <CardDescription>{t('ai.not_configured_desc')}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button onClick={() => onNav('settings')}>{t('ai.go_to_settings')}</Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="flex flex-col h-full p-4 md:p-6 animate-fade-in">
             <h1 className="text-2xl font-bold mb-4">{t('ai.title')}</h1>
            <div className="flex-grow overflow-y-auto mb-4 space-y-4 pr-2">
                {appState.chatHistory.length === 0 && (
                    <div className="text-center text-gray-500 pt-16">
                        <SparklesIcon className="w-12 h-12 mx-auto mb-2" />
                        <p>{t('ai.welcome')}</p>
                        <p className="text-sm">{t('ai.example_prompt')}</p>
                    </div>
                )}
                {appState.chatHistory.map((msg, index) => (
                    <div key={index} className={`flex items-end gap-2 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-xs lg:max-w-xl px-4 py-2 rounded-xl whitespace-pre-wrap ${msg.role === 'user' ? 'bg-blue-600 text-white rounded-br-none' : 'bg-gray-700 text-gray-200 rounded-bl-none'}`}>
                           <p>{msg.text}</p>
                        </div>
                    </div>
                ))}
                {isLoading && (
                    <div className="flex justify-start">
                        <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-xl bg-gray-700 text-gray-200 flex items-center">
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-2"></div>
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-2" style={{animationDelay: '75ms'}}></div>
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '150ms'}}></div>
                        </div>
                    </div>
                )}
                 <div ref={endOfMessagesRef} />
            </div>
            <div className="flex-shrink-0 flex items-center space-x-2">
                <Input 
                    type="text" 
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSend()}
                    placeholder={t('ai.ask_placeholder')}
                    className="flex-1"
                    disabled={isLoading}
                />
                <Button onClick={handleSend} disabled={isLoading || input.trim() === ''} size="icon">
                    <SendIcon className="w-5 h-5" />
                </Button>
            </div>
        </div>
    );
};

const SettingsView = ({ appState, setApiKey, setAiModel, setLanguage, updateUserProfile, setDailyGoal, importData, t }: { appState: AppState; setApiKey: (key: string) => void; setAiModel: (model: string) => void; setLanguage: (lang: 'en' | 'zh-TW') => void; updateUserProfile: (profile: Partial<UserProfile>) => void; setDailyGoal: (goal: number) => void; importData: (state: AppState) => boolean; t: (key: string) => string; }) => {
    const [localState, setLocalState] = useState({
        apiKey: appState.apiKey || '',
        aiModel: appState.aiModel,
        language: appState.language,
        userProfile: appState.userProfile
    });
    const [saveMessage, setSaveMessage] = useState('');
    const importFileRef = useRef<HTMLInputElement>(null);

    const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setLocalState(prev => ({
            ...prev,
            userProfile: { ...prev.userProfile, [name]: value === '' ? null : (name === 'age' || name === 'weight' || name === 'height' ? parseFloat(value) : value) }
        }));
    };
    
    const handleSave = () => {
        setApiKey(localState.apiKey);
        setAiModel(localState.aiModel);
        setLanguage(localState.language);
        updateUserProfile(localState.userProfile);
        setSaveMessage(t('settings.saved'));
        setTimeout(() => setSaveMessage(''), 3000);
    };

    const handleCalculateTDEE = () => {
        const { age, sex, weight, height, activityLevel } = localState.userProfile;
        if (!age || !sex || !weight || !height) {
            alert(t('settings.user_profile.validation_error'));
            return;
        }

        let bmr = 0;
        if (sex === 'male') {
            bmr = 10 * weight + 6.25 * height - 5 * age + 5;
        } else {
            bmr = 10 * weight + 6.25 * height - 5 * age - 161;
        }
        
        const activityMultipliers = {
            sedentary: 1.2, light: 1.375, moderate: 1.55, active: 1.725, 'very_active': 1.9
        };
        const tdee = Math.round(bmr * activityMultipliers[activityLevel]);
        setDailyGoal(tdee);
        alert(`${t('settings.user_profile.tdee_result_1')} ${tdee} ${t('dashboard.kcal')}. ${t('settings.user_profile.tdee_result_2')}`);
    };

    const handleExport = () => {
        const dataStr = JSON.stringify(appState, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        const exportFileDefaultName = `calorie-tracker-backup-${new Date().toISOString().split('T')[0]}.json`;
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    };

    const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (window.confirm(t('settings.data_management.import_confirm'))) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const newState = JSON.parse(event.target?.result as string);
                        if (importData(newState)) {
                            alert(t('settings.data_management.import_success'));
                            window.location.reload();
                        } else {
                            throw new Error('Invalid data format');
                        }
                    } catch (err) {
                        alert(t('settings.data_management.import_error'));
                    }
                };
                reader.readAsText(file);
            }
        }
    };

    return (
        <div className="p-4 md:p-6 space-y-6 animate-fade-in">
            <h1 className="text-2xl font-bold">{t('settings.title')}</h1>
            
            <Card>
                <CardHeader>
                    <CardTitle><UserCircleIcon className="inline-block w-6 h-6 mr-2" />{t('settings.user_profile.title')}</CardTitle>
                    <CardDescription>{t('settings.user_profile.desc')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="age">{t('settings.user_profile.age')}</Label>
                            <Input id="age" name="age" type="number" value={localState.userProfile.age || ''} onChange={handleProfileChange} />
                        </div>
                        <div>
                            <Label htmlFor="sex">{t('settings.user_profile.sex')}</Label>
                            <Select id="sex" name="sex" value={localState.userProfile.sex || ''} onChange={handleProfileChange}>
                                <option value="" disabled>{t('settings.user_profile.select_sex')}</option>
                                <option value="male">{t('settings.user_profile.male')}</option>
                                <option value="female">{t('settings.user_profile.female')}</option>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="weight">{t('settings.user_profile.weight')}</Label>
                            <Input id="weight" name="weight" type="number" value={localState.userProfile.weight || ''} onChange={handleProfileChange} />
                        </div>
                        <div>
                            <Label htmlFor="height">{t('settings.user_profile.height')}</Label>
                            <Input id="height" name="height" type="number" value={localState.userProfile.height || ''} onChange={handleProfileChange} />
                        </div>
                    </div>
                    <div>
                        <Label htmlFor="activityLevel">{t('settings.user_profile.activity_level')}</Label>
                        <Select id="activityLevel" name="activityLevel" value={localState.userProfile.activityLevel || 'moderate'} onChange={handleProfileChange}>
                            <option value="sedentary">{t('settings.user_profile.activity.sedentary')}</option>
                            <option value="light">{t('settings.user_profile.activity.light')}</option>
                            <option value="moderate">{t('settings.user_profile.activity.moderate')}</option>
                            <option value="active">{t('settings.user_profile.activity.active')}</option>
                            <option value="very_active">{t('settings.user_profile.activity.very_active')}</option>
                        </Select>
                    </div>
                     <Button onClick={handleCalculateTDEE} className="bg-purple-600 hover:bg-purple-700">{t('settings.user_profile.calculate_goal')}</Button>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>{t('settings.ai_settings')}</CardTitle>
                    <CardDescription>{t('settings.ai_settings_desc')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label htmlFor="api-key">{t('settings.api_key')}</Label>
                        <Input id="api-key" type="password" value={localState.apiKey} onChange={(e) => setLocalState(p => ({...p, apiKey: e.target.value}))} placeholder={t('settings.api_key_placeholder')} />
                    </div>
                    <div>
                        <Label htmlFor="ai-model">{t('settings.ai_model')}</Label>
                        <Select id="ai-model" value={localState.aiModel} onChange={(e) => setLocalState(p => ({...p, aiModel: e.target.value}))}>
                            <option value="gemini-2.5-flash">{t('settings.model.flash')}</option>
                            <option value="gemini-1.5-pro">{t('settings.model.pro')}</option>
                            <option value="gemini-pro">{t('settings.model.gemini-pro')}</option>
                        </Select>
                    </div>
                    <div>
                        <Label htmlFor="language">{t('settings.language.label')}</Label>
                        <Select id="language" value={localState.language} onChange={(e) => setLocalState(p => ({...p, language: e.target.value as 'en' | 'zh-TW'}))}>
                            <option value="en">{t('settings.language.en')}</option>
                            <option value="zh-TW">{t('settings.language.zh-TW')}</option>
                        </Select>
                    </div>
                </CardContent>
            </Card>
            
            <Card>
                <CardHeader>
                    <CardTitle>{t('settings.data_management.title')}</CardTitle>
                    <CardDescription>{t('settings.data_management.desc')}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col sm:flex-row gap-4">
                    <Button onClick={handleExport} className="w-full sm:w-auto"><DownloadIcon className="w-5 h-5 mr-2"/>{t('settings.data_management.export')}</Button>
                    <Button onClick={() => importFileRef.current?.click()} className="w-full sm:w-auto bg-gray-600 hover:bg-gray-700"><UploadIcon className="w-5 h-5 mr-2"/>{t('settings.data_management.import')}</Button>
                    <input type="file" ref={importFileRef} onChange={handleImport} accept=".json" className="hidden" />
                </CardContent>
            </Card>

            <div className="flex items-center space-x-4 mt-4">
                <Button onClick={handleSave} className="w-full md:w-auto">{t('settings.save')}</Button>
                {saveMessage && <p className="text-green-400 text-sm animate-fade-in">{saveMessage}</p>}
            </div>
        </div>
    );
};





export default function App() {
  const { appState, getLogForDate, addFood, addExercise, removeFood, removeExercise, setDailyGoal, setApiKey, setAiModel, isInitialized, selectedDate, setSelectedDate, setLanguage, updateUserProfile, setChatHistory, importData } = useAppState();
  const [activeView, setActiveView] = useState<ActiveView>('dashboard');
  const { t, isLoaded, currentLanguage, locale } = useTranslation(appState.language);
  const [newVersionAvailable, setNewVersionAvailable] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);
  
  const currentLog = useMemo(() => getLogForDate(getDateString(selectedDate)), [getLogForDate, selectedDate]);

  useEffect(() => {
    document.title = t('appTitle');
    document.documentElement.lang = currentLanguage;
  }, [t, currentLanguage]);

  useEffect(() => {
    const handleNewVersion = (event: Event) => {
      const customEvent = event as CustomEvent<ServiceWorkerRegistration>;
      setWaitingWorker(customEvent.detail.waiting);
      setNewVersionAvailable(true);
    };

    window.addEventListener('new-version-available', handleNewVersion);

    return () => {
      window.removeEventListener('new-version-available', handleNewVersion);
    };
  }, []);

  const handleUpdate = () => {
    if (waitingWorker) {
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
      setNewVersionAvailable(false);
      window.location.reload();
    }
  };

  if (!isInitialized || !isLoaded) {
    return (
        <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
            <div className="flex items-center space-x-2">
                 <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                 <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '150ms'}}></div>
                 <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '300ms'}}></div>
                 <span className="ml-2">{t('loading')}</span>
            </div>
        </div>
    );
  }
  
  const renderView = () => {
    switch (activeView) {
      case 'dashboard':
        return <DashboardView dailyGoal={appState.dailyGoal} logs={appState.logs} selectedDate={selectedDate} setSelectedDate={setSelectedDate} t={t} locale={locale}/>;
      case 'log':
        return <LogView currentLog={currentLog} addFood={addFood} addExercise={addExercise} removeFood={removeFood} removeExercise={removeExercise} appState={appState} t={t} selectedDate={selectedDate} setSelectedDate={setSelectedDate} locale={locale} />;
      case 'ai':
        return <AiAssistantView appState={appState} setChatHistory={setChatHistory} currentLog={currentLog} onNav={setActiveView} t={t} />;
      case 'settings':
        return <SettingsView appState={appState} setApiKey={setApiKey} setAiModel={setAiModel} setLanguage={setLanguage} updateUserProfile={updateUserProfile} setDailyGoal={setDailyGoal} importData={importData} t={t} />;
      default:
        return <DashboardView dailyGoal={appState.dailyGoal} logs={appState.logs} selectedDate={selectedDate} setSelectedDate={setSelectedDate} t={t} locale={locale}/>;
    }
  };

  const navItems = [
      { view: 'dashboard', label: t('nav.dashboard'), Icon: HomeIcon },
      { view: 'log', label: t('nav.log'), Icon: ClipboardIcon },
      { view: 'ai', label: t('nav.ai_assistant'), Icon: SparklesIcon },
      { view: 'settings', label: t('nav.settings'), Icon: SettingsIcon },
  ];

  return (
      <AppLayout>
          {/* Skip Links for Accessibility */}
          <SkipLink href="#main-content">Skip to main content</SkipLink>
          <SkipLink href="#navigation">Skip to navigation</SkipLink>

          {newVersionAvailable && (
            <div className="fixed bottom-4 right-4 bg-success text-success-foreground p-4 rounded-lg shadow-lg z-50 flex items-center gap-4 animate-fade-in">
              <p>{t('update.new_version_available')}</p>
              <Button onClick={handleUpdate} variant="outline" size="sm">
                {t('update.update_now')}
              </Button>
            </div>
          )}

          {/* Desktop Sidebar */}
          <Sidebar>
              <SidebarHeader>
                  <h1 className="heading-4 text-foreground">{t('appTitle')}</h1>
              </SidebarHeader>
              <SidebarContent>
                  <nav id="navigation" aria-label="Main navigation">
                      <Navigation>
                          {navItems.map(item => (
                              <NavigationItem
                                  key={item.view}
                                  icon={<item.Icon className="h-5 w-5" aria-hidden="true" />}
                                  label={item.label}
                                  isActive={activeView === item.view}
                                  onClick={() => setActiveView(item.view as ActiveView)}
                              />
                          ))}
                      </Navigation>
                  </nav>
              </SidebarContent>
          </Sidebar>

          {/* Main Content */}
          <MainContent>
              <MobileHeader>
                  <div className="text-center p-4">
                      <h1 className="heading-5 text-foreground">{t('appTitle')}</h1>
                  </div>
              </MobileHeader>

              <ContentArea>
                  <main id="main-content" aria-label="Main content">
                      {renderView()}
                  </main>
              </ContentArea>
          </MainContent>

          {/* Mobile Bottom Nav */}
          <MobileNavigation>
              <nav aria-label="Mobile navigation">
                  {navItems.map(item => (
                      <MobileNavigationItem
                          key={item.view}
                          icon={<item.Icon className="h-6 w-6" aria-hidden="true" />}
                          label={item.label}
                          isActive={activeView === item.view}
                          onClick={() => setActiveView(item.view as ActiveView)}
                      />
                  ))}
              </nav>
          </MobileNavigation>
      </AppLayout>
  );
}