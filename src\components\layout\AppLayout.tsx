import React from 'react'
import { cn } from '@/src/lib/utils'

interface AppLayoutProps {
  children: React.ReactNode
  className?: string
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "min-h-screen bg-background text-foreground font-sans antialiased",
      "flex flex-col md:flex-row",
      className
    )}>
      {children}
    </div>
  )
}

interface SidebarProps {
  children: React.ReactNode
  className?: string
}

export const Sidebar: React.FC<SidebarProps> = ({ children, className }) => {
  return (
    <aside className={cn(
      "hidden md:flex flex-col w-64 bg-card border-r border-border",
      "shadow-sm",
      className
    )}>
      {children}
    </aside>
  )
}

interface SidebarHeaderProps {
  children: React.ReactNode
  className?: string
}

export const SidebarHeader: React.FC<SidebarHeaderProps> = ({ children, className }) => {
  return (
    <header className={cn(
      "flex items-center justify-center p-6 border-b border-border",
      className
    )}>
      {children}
    </header>
  )
}

interface SidebarContentProps {
  children: React.ReactNode
  className?: string
}

export const SidebarContent: React.FC<SidebarContentProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "flex-1 overflow-y-auto p-4",
      className
    )}>
      {children}
    </div>
  )
}

interface MainContentProps {
  children: React.ReactNode
  className?: string
}

export const MainContent: React.FC<MainContentProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "flex-1 flex flex-col overflow-hidden",
      className
    )}>
      {children}
    </div>
  )
}

interface MobileHeaderProps {
  children: React.ReactNode
  className?: string
}

export const MobileHeader: React.FC<MobileHeaderProps> = ({ children, className }) => {
  return (
    <header className={cn(
      "md:hidden flex-shrink-0 bg-card/80 backdrop-blur-sm border-b border-border",
      "shadow-sm z-10",
      className
    )}>
      {children}
    </header>
  )
}

interface ContentAreaProps {
  children: React.ReactNode
  className?: string
}

export const ContentArea: React.FC<ContentAreaProps> = ({ children, className }) => {
  return (
    <main className={cn(
      "flex-grow overflow-y-auto",
      className
    )}>
      <div className="max-w-7xl mx-auto w-full h-full">
        {children}
      </div>
    </main>
  )
}

interface MobileNavigationProps {
  children: React.ReactNode
  className?: string
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({ children, className }) => {
  return (
    <footer className={cn(
      "md:hidden flex-shrink-0 bg-card/80 backdrop-blur-sm border-t border-border",
      "shadow-lg z-10",
      className
    )}>
      <nav className="flex justify-around items-center h-16 px-2">
        {children}
      </nav>
    </footer>
  )
}

interface NavigationProps {
  children: React.ReactNode
  className?: string
}

export const Navigation: React.FC<NavigationProps> = ({ children, className }) => {
  return (
    <nav className={cn(
      "flex flex-col space-y-2",
      className
    )}>
      {children}
    </nav>
  )
}

interface NavigationItemProps {
  icon: React.ReactNode
  label: string
  isActive?: boolean
  onClick: () => void
  className?: string
  badge?: string | number
}

export const NavigationItem: React.FC<NavigationItemProps> = ({ 
  icon, 
  label, 
  isActive = false, 
  onClick, 
  className,
  badge 
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex items-center w-full p-3 rounded-lg text-sm font-medium transition-all duration-200",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        isActive 
          ? "bg-primary text-primary-foreground shadow-sm" 
          : "text-muted-foreground hover:text-foreground hover:bg-accent",
        className
      )}
    >
      <span className="mr-3 h-5 w-5 flex-shrink-0">
        {icon}
      </span>
      <span className="flex-1 text-left">{label}</span>
      {badge && (
        <span className={cn(
          "ml-2 px-2 py-0.5 text-xs rounded-full",
          isActive 
            ? "bg-primary-foreground/20 text-primary-foreground" 
            : "bg-muted text-muted-foreground"
        )}>
          {badge}
        </span>
      )}
    </button>
  )
}

interface MobileNavigationItemProps {
  icon: React.ReactNode
  label: string
  isActive?: boolean
  onClick: () => void
  className?: string
  badge?: string | number
}

export const MobileNavigationItem: React.FC<MobileNavigationItemProps> = ({ 
  icon, 
  label, 
  isActive = false, 
  onClick, 
  className,
  badge 
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center w-full py-2 px-1 transition-colors duration-200",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        isActive 
          ? "text-primary" 
          : "text-muted-foreground hover:text-foreground",
        className
      )}
    >
      <div className="relative">
        <span className="h-6 w-6 mb-1 flex-shrink-0">
          {icon}
        </span>
        {badge && (
          <span className="absolute -top-1 -right-1 h-4 w-4 bg-destructive text-destructive-foreground text-xs rounded-full flex items-center justify-center">
            {badge}
          </span>
        )}
      </div>
      <span className="text-xs font-medium truncate max-w-full">{label}</span>
    </button>
  )
}
