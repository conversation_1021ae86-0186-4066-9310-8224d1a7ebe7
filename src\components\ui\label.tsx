import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/src/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
  {
    variants: {
      variant: {
        default: "",
        required: "after:content-['*'] after:ml-0.5 after:text-destructive",
        optional: "after:content-['(optional)'] after:ml-1 after:text-muted-foreground after:font-normal",
      },
      size: {
        default: "text-sm",
        sm: "text-xs",
        lg: "text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface LabelProps
  extends React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>,
    VariantProps<typeof labelVariants> {}

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  LabelProps
>(({ className, variant, size, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants({ variant, size }), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

// Form field wrapper component
const FormField = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    label?: string
    required?: boolean
    error?: string
    helperText?: string
    children: React.ReactNode
  }
>(({ className, label, required, error, helperText, children, ...props }, ref) => {
  const fieldId = React.useId()
  
  return (
    <div ref={ref} className={cn("space-y-2", className)} {...props}>
      {label && (
        <Label 
          htmlFor={fieldId}
          variant={required ? "required" : "default"}
        >
          {label}
        </Label>
      )}
      <div>
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId,
          error: !!error,
          helperText: error || helperText,
        })}
      </div>
    </div>
  )
})
FormField.displayName = "FormField"

export { Label, FormField, labelVariants }
